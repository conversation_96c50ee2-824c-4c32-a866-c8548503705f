from flask import Flask, request, jsonify
from flask_cors import CORS
from dotenv import load_dotenv
import os
import requests
import json
import fitz
import faiss
import numpy as np
from langchain.text_splitter import RecursiveCharacterTextSplitter
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor, ProcessPoolExecutor, as_completed
import re
from bs4 import BeautifulSoup
from docx import Document
import tempfile
import email
from email import policy
import extract_msg
from openai import OpenAI
import hashlib
import pickle
import traceback
from functools import lru_cache
import multiprocessing
import asyncio
import aiohttp
from typing import List, Dict, Any

# Try to import CuPy for GPU acceleration
try:
    import cupy as cp
    GPU_AVAILABLE = True
    print("GPU acceleration available with CuPy")
except ImportError:
    cp = None
    GPU_AVAILABLE = False
    print("CuPy not available, using CPU only")

# Try to import faster PDF processing libraries
try:
    import pdfplumber
    PDFPLUMBER_AVAILABLE = True
except ImportError:
    PDFPLUMBER_AVAILABLE = False

try:
    import pypdf
    PYPDF_AVAILABLE = True
except ImportError:
    PYPDF_AVAILABLE = False

load_dotenv()
app = Flask(__name__)
CORS(app)

client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
TEAM_TOKEN = "d1b791fa0ef5092d9cd051b2b09df2473d1e2ea07e09fe6c61abb5722dfbc7d3"

CACHE_DIR = "cache"
os.makedirs(CACHE_DIR, exist_ok=True)

# In-memory cache for frequently accessed data using dict with size limit
memory_cache = {}
MEMORY_CACHE_SIZE = 100

# Performance optimization settings
PERFORMANCE_CONFIG = {
    'use_gpu': GPU_AVAILABLE,
    'max_pdf_workers': min(multiprocessing.cpu_count(), 8),
    'max_chunk_workers': min(multiprocessing.cpu_count(), 8),
    'max_embedding_workers': 4,  # Limit to avoid API rate limits
    'batch_size_embeddings': 100,
    'batch_size_questions': 5,
    'enable_parallel_pdf': True,
    'enable_parallel_chunks': True,
    'enable_parallel_retrieval': True,
}

# Performance monitoring
performance_stats = {
    'pdf_extraction_time': 0,
    'chunk_generation_time': 0,
    'embedding_time': 0,
    'retrieval_time': 0,
    'total_requests': 0,
    'cache_hits': 0,
    'cache_misses': 0
}

def log_performance(operation, duration):
    """Log performance metrics"""
    if operation in performance_stats:
        performance_stats[operation] += duration
    performance_stats['total_requests'] += 1

# Pre-compiled regex patterns for better performance
REGEX_PATTERNS = {
    'page_headers': re.compile(r"Page \d+ of \d+"),
    'whitespace': re.compile(r"\s{2,}"),
    'broken_words': re.compile(r"(\w+)-\s*\n\s*(\w+)"),
    'currency': re.compile(r"\$\s+(\d)"),
    'percentage': re.compile(r"(\d)\s+%"),
    'newlines': re.compile(r"\n{3,}"),
    'definitions': re.compile(r'\b([A-Z][\w\s]+)\s+means\s+([^\.]+\.)', re.MULTILINE),
    'first_sentence': re.compile(r'[.!?]\s'),
    'insurance_keywords': re.compile(r'\b(?:claim|premium|coverage|deductible|exclusion|benefit|policy|insured|limit|condition|amount|liability|copay|coinsurance|network|provider|reimbursement|payment|cost|fee|charge|expense|maximum|minimum|percentage|dollar|annual|monthly|eligible|eligibility|waiting|period|effective|date|termination|renewal|grace|notification|approval|document|required|submission|proof|evidence|terms|clause|diagnosis|treatment|procedure|physician|hospital|prescription|medication|emergency|preventive|specialist|definition|scope|coverage|exclusion|exception|provision|endorsement|schedule|attachment|addendum)\b', re.IGNORECASE)
}


@lru_cache(maxsize=1000)
def clean_text(text):
    # Remove page headers/footers
    text = REGEX_PATTERNS['page_headers'].sub("", text)

    # Preserve important punctuation but clean excessive whitespace
    text = REGEX_PATTERNS['whitespace'].sub(" ", text)

    # Fix broken words across lines (common in PDFs)
    text = REGEX_PATTERNS['broken_words'].sub(r"\1\2", text)

    # Preserve currency and percentage formatting
    text = REGEX_PATTERNS['currency'].sub(r"$\1", text)
    text = REGEX_PATTERNS['percentage'].sub(r"\1%", text)

    # Clean but preserve section markers
    text = REGEX_PATTERNS['newlines'].sub("\n\n", text)

    return text.strip()

@lru_cache(maxsize=1000)
def get_cache_key(url):
    return hashlib.md5(url.encode()).hexdigest()

def manage_memory_cache():
    """Remove oldest items if cache exceeds size limit"""
    if len(memory_cache) > MEMORY_CACHE_SIZE:
        # Remove 20% of oldest items
        items_to_remove = len(memory_cache) - int(MEMORY_CACHE_SIZE * 0.8)
        for _ in range(items_to_remove):
            memory_cache.pop(next(iter(memory_cache)))

def save_cache(cache_key, data):
    # Save to memory cache first for faster access
    memory_cache[cache_key] = data
    manage_memory_cache()

    # Also save to disk for persistence
    try:
        with open(os.path.join(CACHE_DIR, f"{cache_key}.pkl"), "wb") as f:
            pickle.dump(data, f)
    except Exception:
        pass  # Don't fail if disk cache fails

def load_cache(cache_key):
    # Check memory cache first
    if cache_key in memory_cache:
        performance_stats['cache_hits'] += 1
        return memory_cache[cache_key]

    # Fall back to disk cache
    path = os.path.join(CACHE_DIR, f"{cache_key}.pkl")
    if os.path.exists(path):
        try:
            with open(path, "rb") as f:
                data = pickle.load(f)
                # Store in memory cache for next time
                memory_cache[cache_key] = data
                manage_memory_cache()
                performance_stats['cache_hits'] += 1
                return data
        except Exception:
            pass  # Don't fail if disk cache is corrupted

    performance_stats['cache_misses'] += 1
    return None


def extract_pdf_text_parallel(pdf_path):
    """Extract text from PDF using parallel processing for better performance"""
    def extract_page_text(page_num):
        try:
            doc = fitz.open(pdf_path)
            page = doc[page_num]
            text = page.get_text()
            doc.close()
            return page_num, clean_text(text)
        except Exception as e:
            return page_num, f"Error extracting page {page_num}: {str(e)}"

    # Get total page count
    doc = fitz.open(pdf_path)
    total_pages = len(doc)
    doc.close()

    # Use parallel processing for large PDFs
    if total_pages > 10:
        max_workers = min(multiprocessing.cpu_count(), total_pages, 8)
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [executor.submit(extract_page_text, i) for i in range(total_pages)]
            results = [future.result() for future in futures]

        # Sort by page number and extract text
        results.sort(key=lambda x: x[0])
        return [result[1] for result in results]
    else:
        # For small PDFs, use sequential processing
        doc = fitz.open(pdf_path)
        texts = [clean_text(page.get_text()) for page in doc]
        doc.close()
        return texts

def extract_pdf_text_fast(pdf_path):
    """Try multiple PDF extraction methods for best performance and accuracy"""
    methods = []

    # Method 1: pdfplumber (most accurate for complex layouts)
    if PDFPLUMBER_AVAILABLE:
        methods.append(("pdfplumber", lambda: extract_with_pdfplumber(pdf_path)))

    # Method 2: PyMuPDF parallel (fastest for simple layouts)
    methods.append(("pymupdf_parallel", lambda: extract_pdf_text_parallel(pdf_path)))

    # Method 3: pypdf (fallback)
    if PYPDF_AVAILABLE:
        methods.append(("pypdf", lambda: extract_with_pypdf(pdf_path)))

    # Try methods in order of preference
    for method_name, method_func in methods:
        try:
            result = method_func()
            if result and any(len(text.strip()) > 50 for text in result):
                return result
        except Exception as e:
            print(f"PDF extraction method {method_name} failed: {e}")
            continue

    # Final fallback to basic PyMuPDF
    try:
        doc = fitz.open(pdf_path)
        texts = [clean_text(page.get_text()) for page in doc]
        doc.close()
        return texts
    except Exception as e:
        raise ValueError(f"All PDF extraction methods failed: {e}")

def extract_with_pdfplumber(pdf_path):
    """Extract text using pdfplumber for better table and layout handling"""
    import pdfplumber
    texts = []
    with pdfplumber.open(pdf_path) as pdf:
        for page in pdf.pages:
            text = page.extract_text()
            if text:
                texts.append(clean_text(text))
            else:
                texts.append("")
    return texts

def extract_with_pypdf(pdf_path):
    """Extract text using pypdf as fallback"""
    import pypdf
    texts = []
    with open(pdf_path, 'rb') as file:
        pdf_reader = pypdf.PdfReader(file)
        for page in pdf_reader.pages:
            text = page.extract_text()
            texts.append(clean_text(text))
    return texts

def extract_text_from_url(url):
    """Optimized document text extraction with parallel processing"""
    try:
        # Fetch document with optimized settings
        response = requests.get(url, timeout=60, stream=True)
        response.raise_for_status()
        content_type = response.headers.get("Content-Type", "").lower()

        # Extract filename from URL
        from urllib.parse import urlparse
        parsed_url = urlparse(url)
        path_parts = parsed_url.path.split('/')
        filename = path_parts[-1].lower() if path_parts and path_parts[-1] else "document"

        # Create temporary file
        with tempfile.NamedTemporaryFile(delete=False) as tmp:
            for chunk in response.iter_content(chunk_size=8192):
                tmp.write(chunk)
            tmp_path = tmp.name
    except Exception as e:
        raise e

    try:
        if "pdf" in content_type or filename.endswith(".pdf"):
            return extract_pdf_text_fast(tmp_path)

        elif "word" in content_type or filename.endswith(".docx"):
            doc = Document(tmp_path)
            return ["\n".join(clean_text(p.text) for p in doc.paragraphs)]

        elif "text/plain" in content_type or filename.endswith(".txt"):
            return [clean_text(response.text)]

        elif "html" in content_type or filename.endswith(".html"):
            soup = BeautifulSoup(response.text, "lxml")
            return [clean_text(soup.get_text(separator="\n"))]

        elif filename.endswith(".eml"):
            with open(tmp_path, "rb") as f:
                msg = email.message_from_binary_file(f, policy=policy.default)
            body = ""
            if msg.is_multipart():
                for part in msg.walk():
                    if part.get_content_type() == "text/plain":
                        body += part.get_payload(decode=True).decode(errors="ignore")
            else:
                body = msg.get_payload(decode=True).decode(errors="ignore")
            return [clean_text(body)]

        elif filename.endswith(".msg"):
            msg = extract_msg.Message(tmp_path)
            return [clean_text(msg.body)]

        else:
            raise ValueError("Unsupported document type or unknown format.")

    finally:
        # Clean up temporary file
        try:
            os.unlink(tmp_path)
        except:
            pass


def process_page_chunks(args):
    """Process a single page for chunk generation - designed for parallel processing"""
    page_num, page_text, splitter = args

    if not page_text.strip():
        return []

    # Pre-process the page text to identify key definition sections (optimized)
    definition_sections = []
    definitions = REGEX_PATTERNS['definitions'].finditer(page_text)
    for match in definitions:
        definition_sections.append((match.start(), match.end(), match.group(0)))

    page_chunks = splitter.split_text(page_text)
    chunks = []

    for i, chunk in enumerate(page_chunks):
        if len(chunk.strip()) < 50:
            continue

        # Add more context to the prefix for better identification (optimized)
        first_sentence_match = REGEX_PATTERNS['first_sentence'].split(chunk.strip())
        first_sentence = first_sentence_match[0][:100] if first_sentence_match else chunk.strip()[:100]
        context_prefix = f"Page {page_num}, Section {i+1}: {first_sentence}... "

        # Check for insurance-specific patterns using pre-compiled regex
        chunk_lower = chunk.lower()
        contains_definition = 'means' in chunk_lower and bool(re.search(r'\b\w+\s+means\b', chunk_lower))
        contains_exclusion = bool(re.search(r'\bexclusion|\bexcluded|\bnot covered|\bnot eligible', chunk_lower))
        contains_coverage = bool(re.search(r'\bcoverage|\bcovered|\beligible|\bincluded', chunk_lower))
        contains_limit = bool(re.search(r'\blimit|\bcap|\bmaximum|\bupto|\bup to', chunk_lower))
        contains_condition = bool(re.search(r'\bcondition|\bprovided that|\bsubject to|\bif and only if', chunk_lower))

        metadata = []
        if contains_definition: metadata.append("definition")
        if contains_exclusion: metadata.append("exclusion")
        if contains_coverage: metadata.append("coverage")
        if contains_limit: metadata.append("limit")
        if contains_condition: metadata.append("condition")

        chunks.append({
            "text": context_prefix + chunk,
            "page": page_num,
            "section": i+1,
            "raw_text": chunk,
            "metadata": metadata
        })

    return chunks

def generate_smart_chunks(text_by_page):
    """Optimized chunk generation with parallel processing"""
    # Enhanced insurance-specific separators for better boundary detection
    separators = [
        # Document structure markers (highest priority)
        "\n\nARTICLE", "\n\nSECTION", "\n\nCLAUSE", "\n\nPART",
        "\n\nCOVERAGE", "\n\nBENEFIT", "\n\nEXCLUSION", "\n\nLIMIT",
        "\n\nArticle", "\n\nSection", "\n\nClause", "\n\nPart",
        "\n\nCoverage", "\n\nBenefit", "\n\nExclusion", "\n\nLimit",

        # Numbered sections (common in legal documents)
        r"\n\d+\.\d+", r"\n\d+\.", r"\n[A-Z]\.", r"\n[a-z]\.", r"\n[ivxIVX]+\.",

        # Definitions and key terms
        "\n\nDefinitions", "\n\nTerms", "\n\nGlossary",

        # General document separators
        "\n\n", "\n", ". ", "; ", ", ", " "
    ]

    # Determine chunk size and overlap based on PDF page count
    page_count = len(text_by_page)
    if page_count >= 100:
        chunk_size = 1200
        chunk_overlap = 150
    else:
        chunk_size = 800
        chunk_overlap = 100

    # Optimized chunk size and overlap for better context preservation
    splitter = RecursiveCharacterTextSplitter(
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap,
        separators=separators
    )

    # Use parallel processing for large documents
    if len(text_by_page) > 20:  # Parallel processing for documents with more than 20 pages
        max_workers = min(multiprocessing.cpu_count(), len(text_by_page), 8)

        # Prepare arguments for parallel processing
        args_list = [(page_num, page_text, splitter)
                     for page_num, page_text in enumerate(text_by_page, 1)]

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            results = list(executor.map(process_page_chunks, args_list))

        # Flatten the results
        chunks = []
        for page_chunks in results:
            chunks.extend(page_chunks)
    else:
        # Sequential processing for smaller documents
        chunks = []
        for page_num, page_text in enumerate(text_by_page, 1):
            page_chunks = process_page_chunks((page_num, page_text, splitter))
            chunks.extend(page_chunks)

    return chunks


def embed_chunks_openai_parallel(chunks):
    """Parallel embedding generation with GPU acceleration"""
    texts = [c["text"] for c in chunks]

    def process_batch(batch_texts):
        """Process a single batch of texts"""
        try:
            resp = client.embeddings.create(model="text-embedding-3-large", input=batch_texts)
            return [d.embedding for d in resp.data]
        except Exception as e:
            print(f"Batch processing error: {e}")
            return []

    try:
        # Optimize batch size based on text length and available resources
        avg_text_length = sum(len(text) for text in texts) / len(texts) if texts else 0
        if avg_text_length > 2000:
            batch_size = 50  # Smaller batches for longer texts
        else:
            batch_size = 100  # Standard batch size

        all_embeddings = []

        # Process batches in parallel for better throughput
        if len(texts) > batch_size * 2:  # Only use parallel processing for larger datasets
            max_workers = min(4, (len(texts) // batch_size) + 1)  # Limit concurrent API calls
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                futures = []
                for i in range(0, len(texts), batch_size):
                    batch_texts = texts[i:i + batch_size]
                    future = executor.submit(process_batch, batch_texts)
                    futures.append(future)

                for future in futures:
                    batch_embeddings = future.result()
                    all_embeddings.extend(batch_embeddings)
        else:
            # Sequential processing for smaller datasets
            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i:i + batch_size]
                batch_embeddings = process_batch(batch_texts)
                all_embeddings.extend(batch_embeddings)

        # Convert to appropriate array format (GPU if available)
        if GPU_AVAILABLE and len(all_embeddings) > 1000:  # Use GPU for large datasets
            embeddings = cp.array(all_embeddings, dtype=cp.float32)
            norms = cp.linalg.norm(embeddings, axis=1)[:, None]
            norm_embeddings = embeddings / norms
            # Convert back to CPU for FAISS (FAISS-GPU setup would be more complex)
            norm_embeddings = cp.asnumpy(norm_embeddings)
        else:
            embeddings = np.array(all_embeddings, dtype=np.float32)
            norm_embeddings = embeddings / np.linalg.norm(embeddings, axis=1)[:, None]

        # Build optimized FAISS index
        dimension = norm_embeddings.shape[1]

        # Use different index types based on dataset size
        if len(norm_embeddings) > 10000:
            # Use IVF index for large datasets
            nlist = min(int(np.sqrt(len(norm_embeddings))), 1000)
            quantizer = faiss.IndexFlatIP(dimension)
            index = faiss.IndexIVFFlat(quantizer, dimension, nlist)
            index.train(norm_embeddings)
        else:
            # Use flat index for smaller datasets
            index = faiss.IndexFlatIP(dimension)

        index.add(norm_embeddings)

        return index, chunks, norm_embeddings
    except Exception as e:
        raise e

# Alias for backward compatibility
embed_chunks_openai = embed_chunks_openai_parallel


def compute_similarity_scores_gpu(query_vec, chunk_embeddings):
    """Compute similarity scores using GPU acceleration if available"""
    if GPU_AVAILABLE and len(chunk_embeddings) > 1000:
        # Use GPU for large datasets
        query_gpu = cp.array(query_vec, dtype=cp.float32)
        embeddings_gpu = cp.array(chunk_embeddings, dtype=cp.float32)

        # Compute dot product similarity
        scores = cp.dot(embeddings_gpu, query_gpu)

        # Convert back to CPU
        return cp.asnumpy(scores)
    else:
        # Use CPU for smaller datasets
        return np.dot(chunk_embeddings, query_vec)

def insurance_specific_retrieve_fast(question, index, chunks, k=8):
    """Optimized retrieval with GPU acceleration and parallel processing"""
    # Query embedding with same model
    try:
        q_resp = client.embeddings.create(model="text-embedding-3-large", input=question)
        q_vec = np.array(q_resp.data[0].embedding, dtype=np.float32)
        q_vec = q_vec / np.linalg.norm(q_vec)
    except Exception as e:
        raise e

    # Get more candidates first (optimized search)
    search_k = min(k * 4, len(chunks))  # Don't search for more chunks than available
    scores, indices = index.search(np.array([q_vec]), search_k)

    # Pre-defined insurance keywords set for faster lookup
    insurance_keywords = {
        'claim', 'premium', 'coverage', 'deductible', 'exclusion',
        'benefit', 'policy', 'insured', 'limit', 'condition', 'amount',
        'liability', 'copay', 'coinsurance', 'network', 'provider',
        'reimbursement', 'payment', 'cost', 'fee', 'charge', 'expense',
        'maximum', 'minimum', 'percentage', 'dollar', 'annual', 'monthly',
        'eligible', 'eligibility', 'waiting', 'period', 'effective', 'date',
        'termination', 'renewal', 'grace', 'notification', 'approval', 'document',
        'required', 'submission', 'proof', 'evidence', 'terms', 'clause',
        'diagnosis', 'treatment', 'procedure', 'physician', 'hospital',
        'prescription', 'medication', 'emergency', 'preventive', 'specialist',
        'definition', 'scope', 'coverage', 'exclusion', 'exception', 'provision',
        'endorsement', 'schedule', 'attachment', 'addendum'
    }

    # Extract key terms from question (optimized)
    question_lower = question.lower()
    question_words = set(re.findall(r"\w+", question_lower))
    question_words_list = list(question_words)
    question_bigrams = set([' '.join(pair) for pair in zip(question_words_list, question_words_list[1:])])

    # Pre-compute question characteristics for efficiency
    question_has_numbers = bool(re.search(r'\$|%|\d+|amount|cost|fee|limit', question_lower))
    is_definition_question = any(word in question_lower for word in ['what is', 'define', 'meaning'])

    def score_chunk(args):
        """Score a single chunk - designed for parallel processing"""
        rank, i = args
        chunk = chunks[i]
        raw_text = chunk["raw_text"]
        text_lower = raw_text.lower()

        # Optimized keyword extraction and matching
        text_words = set(re.findall(r"\w+", text_lower))
        common_keywords = question_words & text_words
        insurance_terms = common_keywords & insurance_keywords

        # Check for exact phrase matches (optimized)
        phrase_matches = sum(1 for word in question_words
                           if len(word) > 3 and word in text_lower)

        # Check for bigram matches (optimized)
        bigram_matches = sum(3 for bigram in question_bigrams if bigram in text_lower)

        # Check for numerical/financial content matches
        has_numbers = bool(re.search(r'\$|%|\d+', raw_text))
        number_bonus = 0.15 if (has_numbers and question_has_numbers) else 0

        # Look for definition patterns ("X means Y")
        definition_pattern = 'means' in text_lower and bool(re.search(r'\b\w+\s+means\b', text_lower))
        definition_bonus = 0.2 if (definition_pattern and is_definition_question) else 0

        keyword_score = len(common_keywords) + (len(insurance_terms) * 2) + phrase_matches + bigram_matches
        semantic_score = scores[0][rank]

        # Enhanced scoring formula
        final_score = (0.7 * semantic_score) + \
                      (0.15 * min(keyword_score / 10.0, 1.0)) + \
                      number_bonus + \
                      definition_bonus

        return {
            "text": chunk["text"],
            "raw_text": raw_text,
            "page": chunk["page"],
            "score": final_score
        }

    # Use parallel processing for scoring if we have many candidates
    if len(indices[0]) > 20:
        max_workers = min(4, len(indices[0]))
        args_list = [(rank, i) for rank, i in enumerate(indices[0])]

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            top_matches = list(executor.map(score_chunk, args_list))
    else:
        # Sequential processing for smaller result sets
        top_matches = [score_chunk((rank, i)) for rank, i in enumerate(indices[0])]

    # Sort and return top results
    top_matches = sorted(top_matches, key=lambda x: x["score"], reverse=True)
    return top_matches[:k]

# Alias for backward compatibility
insurance_specific_retrieve = insurance_specific_retrieve_fast


def validate_context_relevance(question, context_chunks, min_relevance=0.25):
    """Enhanced filtering of context chunks based on semantic relevance"""
    question_words = set(re.findall(r'\w+', question.lower()))
    
    # Identify key question types to prioritize different content
    is_definition_question = bool(re.search(r'\bwhat is|\bdefine|\bmeaning|\bdefin[ei]|\bconcept', question.lower()))
    is_coverage_question = bool(re.search(r'\bcover|\bprovide|\binclude|\beligible', question.lower()))
    is_exclusion_question = bool(re.search(r'\bexclude|\bnot cover|\bdeny|\breject', question.lower()))
    is_process_question = bool(re.search(r'\bhow|\bprocess|\bprocedure|\bsteps|\bsubmit', question.lower()))
    is_document_question = bool(re.search(r'\bdocument|\bproof|\bevidence|\breceipt|\bform', question.lower()))
    is_limit_question = bool(re.search(r'\blimit|\bmaximum|\bminimum|\bcap|\bceiling|\bamount', question.lower()))
    
    relevant_chunks = []
    for chunk in context_chunks:
        chunk_words = set(re.findall(r'\w+', chunk['raw_text'].lower()))
        chunk_text = chunk['raw_text'].lower()
        
        # Basic overlap score
        overlap = len(question_words & chunk_words)
        relevance = overlap / max(len(question_words), 1)
        
        # Content type bonuses
        metadata = chunk.get('metadata', [])
        
        # Give bonuses to chunks that match the question type
        type_bonus = 0
        if is_definition_question and ('definition' in metadata or bool(re.search(r'\bmeans\b|\bis defined as\b', chunk_text))):
            type_bonus += 0.3
        if is_coverage_question and ('coverage' in metadata or bool(re.search(r'\bcovered\b|\beligible\b', chunk_text))):
            type_bonus += 0.3
        if is_exclusion_question and ('exclusion' in metadata or bool(re.search(r'\bexcluded\b|\bnot covered\b', chunk_text))):
            type_bonus += 0.3
        if is_process_question and bool(re.search(r'\bsteps\b|\bprocess\b|\bprocedure\b', chunk_text)):
            type_bonus += 0.3
        if is_document_question and bool(re.search(r'\bdocument\b|\bproof\b|\bevidence\b|\bform\b', chunk_text)):
            type_bonus += 0.3
        if is_limit_question and ('limit' in metadata or bool(re.search(r'\blimit\b|\bmaximum\b|\bcap\b', chunk_text))):
            type_bonus += 0.3
            
        # Final relevance score with bonus
        final_relevance = relevance + type_bonus
        
        if final_relevance >= min_relevance or len(relevant_chunks) < 2:  # Always keep at least 2
            relevant_chunks.append({
                **chunk,
                "relevance_score": final_relevance
            })
    
    # Sort by relevance score and limit
    relevant_chunks = sorted(relevant_chunks, key=lambda x: x.get("relevance_score", 0), reverse=True)
    return relevant_chunks[:7]  # Limit to top 7 most relevant for more context


def build_insurance_prompt(question, context_chunks):
    """
    Builds a prompt that instructs the LLM to answer as a knowledgeable insurance assistant,
    using only the provided context, and replying in a clear, concise tone while extracting
    complete financial or legal clauses if present.

    Parameters:
        question (str): The user's question.
        context_chunks (list of dict): List of context dictionaries with a 'text' key.

    Returns:
        str: The formatted prompt to send to the LLM.
    """
    context = "\n---\n".join([c["text"] for c in context_chunks])
    return f"""
You are a helpful insurance assistant explaining policy details in simple terms. Answer questions about insurance policies in a friendly, conversational tone.

*CRITICAL REQUIREMENTS:*
- Keep responses short and concise (1-2 lines whenever possible)
- Use plain, everyday language instead of technical insurance jargon
- Include only the most essential information like key numbers, conditions, and limits
- Be direct and get straight to the point
- Never add information not found in the context

*RESPONSE STYLE EXAMPLES:*
- "An accident is any sudden, unexpected event caused by something external and visible."
- "Children up to 23 years old can be covered if they depend financially on you."
- "If you're permanently disabled, you'll get 100% of your insured amount."

*CONTEXT FROM POLICY DOCUMENT:*
{context}

*QUESTION:* {question}

Provide a short, friendly answer using simple language. Respond in JSON format:
{{ "answer": "..." }}
"""


def call_gpt_fast(prompt):
    try:
        response = client.chat.completions.create(
            model="gpt-4.1-mini",  # Faster and more cost-effective
            messages=[{"role": "user", "content": prompt}],
            temperature=0.1,  # More deterministic for consistent outputs
            max_tokens=600,  # Reduced tokens for faster response
            top_p=0.1,  # More focused on highest probability tokens
            response_format={"type": "json_object"}  # Force JSON output format
        )
        content = response.choices[0].message.content

        try:
            # Parse the JSON response
            parsed_json = json.loads(content)
            answer = parsed_json.get("answer")
            if answer and answer.strip():
                return answer
        except json.JSONDecodeError:
            # Optimized fallback extraction
            if '{"answer":' in content:
                try:
                    start = content.find('{"answer":')
                    end = content.rfind('}') + 1
                    json_str = content[start:end]
                    return json.loads(json_str).get("answer", "Not found in document.")
                except:
                    pass

            # Last resort: regex extraction
            answer_match = re.search(r'"answer"\s*:\s*"([^"]+)"', content)
            if answer_match:
                return answer_match.group(1)

        return "Not found in document."
    except Exception as e:
        return f"Error: {str(e)}"


@app.route("/api/v1/hackrx/run", methods=["POST", "OPTIONS"])
def run_submission():
    # Handle CORS preflight requests
    if request.method == "OPTIONS":
        headers = {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
        return '', 204, headers
    
    try:
        auth_header = request.headers.get("Authorization", "")
        if not auth_header.startswith("Bearer ") or auth_header.split(" ")[1] != TEAM_TOKEN:
            return jsonify({"error": "Unauthorized"}), 401
        
        # Always use an extremely lenient JSON parser
        try:
            # First attempt: Use Flask's built-in parser with force=True
            try:
                data = request.get_json(force=True)
                if data:
                    pass
            except:
                # Second attempt: Try to parse raw data directly
                try:
                    # Try to decode with different encodings
                    try:
                        raw_data = request.data.decode('utf-8')
                    except UnicodeDecodeError:
                        try:
                            raw_data = request.data.decode('latin-1')
                        except:
                            raw_data = request.data.decode('utf-8', errors='replace')
                    
                    # Extract the URL and questions using regex (most reliable for malformed JSON)
                    
                    # Look for URL in the document field (handles both quoted and unquoted)
                    # More comprehensive URL pattern that handles query params with special characters
                    url_pattern = r'documents"?\s*:(?:\s*")?((https?://[^"\s,}]+?)(?:\\"|"|\s|,|}|$))'
                    document_match = re.search(url_pattern, raw_data, re.IGNORECASE)
                    
                    # Look for questions array
                    questions_pattern = r'questions"?\s*:\s*\[(.*?)\]'
                    questions_match = re.search(questions_pattern, raw_data, re.DOTALL)
                    
                    if document_match and questions_match:
                        # Extract document URL - group(1) contains the full URL match
                        document_url = document_match.group(1).split('"')[0].split('\\')[0]
                        # Extract and clean up questions
                        questions_text = questions_match.group(1)
                        questions = []
                        # Extract individual questions with quotes (handles both single and double quotes)
                        for match in re.finditer(r'"([^"]+)"|\'([^\']+)\'', questions_text):
                            if match.group(1):  # Double quotes match
                                questions.append(match.group(1))
                            else:  # Single quotes match
                                questions.append(match.group(2))
                                
                        data = {"documents": document_url, "questions": questions}
                    else:
                        # If regex fails, try standard JSON parsing with cleaning
                        cleaned = raw_data.strip()
                        # Fix common JSON formatting issues
                        cleaned = re.sub(r'([{,])\s*(\w+)\s*:', r'\1"\2":', cleaned)
                        data = json.loads(cleaned)
                
                except Exception as inner_e:
                    error_msg = f"All parsing attempts failed: {str(inner_e)}"
                    return jsonify({"error": "Invalid JSON format", "details": error_msg}), 400
                    
        except Exception as e:
            error_msg = f"JSON parsing error: {str(e)}"
            return jsonify({"error": "Invalid JSON format", "details": error_msg}), 400
            
        document_url = data.get("documents")
        questions = data.get("questions")

        if not document_url or not questions:
            return jsonify({"error": "Missing 'documents' or 'questions'"}), 400

        
        cache_key = get_cache_key(document_url)
        cached = load_cache(cache_key)

        if cached:
            index, chunks = cached["index"], cached["chunks"]
        else:
            
            text_by_page = extract_text_from_url(document_url)
            chunk_dicts = generate_smart_chunks(text_by_page)
            
            if not chunk_dicts:
                return jsonify({"error": "No valid content extracted from document"}), 400
                
            index, chunks, _ = embed_chunks_openai(chunk_dicts)
            save_cache(cache_key, {"index": index, "chunks": chunks})


        def process_question_batch(question_batch):
            """Process a batch of questions for better efficiency"""
            batch_results = []
            for q in question_batch:
                try:
                    top_chunks = insurance_specific_retrieve(q, index, chunks)
                    # Validate and filter context for better accuracy
                    relevant_chunks = validate_context_relevance(q, top_chunks)
                    prompt = build_insurance_prompt(q, relevant_chunks)
                    answer = call_gpt_fast(prompt)
                    batch_results.append(answer)
                except Exception as e:
                    batch_results.append(f"Error processing question: {str(e)}")
            return batch_results

        def process_question_single(q):
            """Process a single question"""
            try:
                top_chunks = insurance_specific_retrieve(q, index, chunks)
                # Validate and filter context for better accuracy
                relevant_chunks = validate_context_relevance(q, top_chunks)
                prompt = build_insurance_prompt(q, relevant_chunks)
                return call_gpt_fast(prompt)
            except Exception as e:
                return f"Error processing question: {str(e)}"

        # Optimize processing strategy based on number of questions
        if len(questions) <= 3:
            # For small number of questions, use parallel processing
            max_workers = min(len(questions), 3)
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                answers = list(executor.map(process_question_single, questions))
        elif len(questions) <= 10:
            # For medium number of questions, use optimized parallel processing
            max_workers = min(len(questions), 5)
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                answers = list(executor.map(process_question_single, questions))
        else:
            # For large number of questions, use batch processing to avoid API rate limits
            batch_size = 5
            all_answers = []

            for i in range(0, len(questions), batch_size):
                batch = questions[i:i + batch_size]
                batch_answers = process_question_batch(batch)
                all_answers.extend(batch_answers)

            answers = all_answers
        
        return jsonify({"answers": answers}), 200

    except Exception as e:
        error_msg = f"Server error: {str(e)}"
        return jsonify({"error": "Server error", "details": error_msg}), 500


@app.route("/health", methods=["GET"])
def health_check():
    return jsonify({
        "status": "healthy",
        "gpu_available": GPU_AVAILABLE,
        "pdfplumber_available": PDFPLUMBER_AVAILABLE,
        "pypdf_available": PYPDF_AVAILABLE,
        "cpu_count": multiprocessing.cpu_count(),
        "performance_config": PERFORMANCE_CONFIG
    }), 200

@app.route("/performance", methods=["GET"])
def performance_stats_endpoint():
    """Get performance statistics"""
    stats = performance_stats.copy()

    # Calculate derived metrics
    if stats['total_requests'] > 0:
        stats['cache_hit_rate'] = stats['cache_hits'] / (stats['cache_hits'] + stats['cache_misses'])
        stats['avg_pdf_extraction_time'] = stats['pdf_extraction_time'] / stats['total_requests']
        stats['avg_chunk_generation_time'] = stats['chunk_generation_time'] / stats['total_requests']
        stats['avg_embedding_time'] = stats['embedding_time'] / stats['total_requests']
        stats['avg_retrieval_time'] = stats['retrieval_time'] / stats['total_requests']

    return jsonify({
        "performance_stats": stats,
        "memory_cache_size": len(memory_cache),
        "memory_cache_limit": MEMORY_CACHE_SIZE
    }), 200


@app.route("/debug/json", methods=["POST"])
def debug_json():
    """Debug endpoint to check JSON parsing"""
    try:
        # Raw data
        raw_data = request.data.decode('utf-8')
        
        # Try all parsing methods
        parsed = {}
        
        # Method 1: Standard JSON parsing
        try:
            parsed["standard"] = json.loads(raw_data)
        except Exception as e:
            parsed["standard_error"] = str(e)
        
        # Method 2: Flask's built-in parser
        try:
            parsed["flask"] = request.get_json(force=True)
        except Exception as e:
            parsed["flask_error"] = str(e)
        
        # Method 3: Cleaned regex approach
        try:
            cleaned_data = raw_data.strip()
            cleaned_data = re.sub(r'"\s*:\s*"', '":"', cleaned_data)
            cleaned_data = re.sub(r'"\s*,\s*"', '","', cleaned_data)
            cleaned_data = re.sub(r'([{,])\s*(\w+)\s*:', r'\1"\2":', cleaned_data)
            parsed["cleaned"] = json.loads(cleaned_data)
        except Exception as e:
            parsed["cleaned_error"] = str(e)
            parsed["cleaned_data"] = cleaned_data
        
        # Return all results
        return jsonify({
            "raw_data": raw_data,
            "parsing_results": parsed,
            "content_type": request.content_type,
            "is_json": request.is_json
        })
    except Exception as e:
        return jsonify({"error": str(e)}), 500


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=int(os.environ.get("PORT", 5000)))